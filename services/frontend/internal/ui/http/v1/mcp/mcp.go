// Package mcp provides Model Context Protocol (MCP) server implementation
// for the SA Intranet application.
//
// This package implements an MCP server that exposes tools for AI assistants
// to interact with the SA Intranet system. Currently includes:
//
//   - greeting: A tool that generates personalized greeting messages
//
// The MCP server is mounted at the /mcp endpoint and supports both GET and POST
// requests for MCP protocol communication.
//
// Example usage:
//
//	// The greeting tool can be called via MCP protocol with:
//	{
//	  "jsonrpc": "2.0",
//	  "id": 1,
//	  "method": "tools/call",
//	  "params": {
//	    "name": "greeting",
//	    "arguments": {
//	      "name": "Alice"
//	    }
//	  }
//	}
//
//	// Response:
//	{
//	  "jsonrpc": "2.0",
//	  "id": 1,
//	  "result": {
//	    "content": [
//	      {
//	        "type": "text",
//	        "text": "Hello, <PERSON>! Welcome to SA Intranet!"
//	      }
//	    ]
//	  }
//	}
package mcp

import (
	"context"
	"net/http"

	"app/frontend/internal/ui"

	"github.com/modelcontextprotocol/go-sdk/auth"
	"github.com/modelcontextprotocol/go-sdk/mcp"
)

// GreetingInput defines the input parameters for the greeting tool
type GreetingInput struct {
	Name string `json:"name" jsonschema:"the name of the person to greet"`
}

// GreetingOutput defines the output structure for the greeting tool
type GreetingOutput struct {
	Greeting string `json:"greeting" jsonschema:"the greeting message"`
}

// greetingHandler handles the greeting tool call
func greetingHandler(ctx context.Context, req *mcp.CallToolRequest, input GreetingInput) (*mcp.CallToolResult, GreetingOutput, error) {
	greeting := "Hello, " + input.Name + "! Welcome to SA Intranet!"

	return &mcp.CallToolResult{
		Content: []mcp.Content{
			&mcp.TextContent{Text: greeting},
		},
	}, GreetingOutput{Greeting: greeting}, nil
}

func RegisterRoutes(app *ui.App) {
	// --- MCP Server setup ---
	impl := &mcp.Implementation{
		Name:    "my-mcp",
		Title:   "Example MCP Server mounted on existing mux",
		Version: "1.0.0",
	}

	// Create the MCP server with your implementation
	srv := mcp.NewServer(impl, &mcp.ServerOptions{})

	jwtAuth := auth.RequireBearerToken(func(ctx context.Context, token string, req *http.Request) (*auth.TokenInfo, error) {
		// return &auth.TokenInfo{}, nil
		return nil, auth.ErrInvalidToken
	}, &auth.RequireBearerTokenOptions{
		ResourceMetadataURL: "http://localhost:8000/.well-known/oauth-protected-resource",
	})

	mcpHandler := mcp.NewStreamableHTTPHandler(func(req *http.Request) *mcp.Server {
		return srv
	}, nil)

	authHandler := jwtAuth(mcpHandler)

	// Register tools/resources/etc. on the server as needed
	mcp.AddTool(srv, &mcp.Tool{
		Name:        "greeting",
		Description: "Generate a personalized greeting message",
	}, greetingHandler)

	// Create the HTTP handler for the MCP server

	middleware := func(next http.Handler) http.Handler {
		return authHandler
	}

	// Mount the MCP handler at /mcp
	app.Router().GET("/mcp", nil, middleware)
	app.Router().POST("/mcp", nil, middleware)

	app.Router().GET("/.well-known/oauth-protected-resource", func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Header().Set("Content-Type", "application/json")
		w.Write([]byte(`{
		  "resource": "` + app.AppConfig.OAUTH_PROTECTED_RESOURCE_URL + `",
		  "authorization_servers": [
		    "` + app.AppConfig.OAUTH_PROTECTED_RESOURCE_SERVER + `"
		  ]
		}`))
	})
}
